"""
measure_lwh_obb.py
~~~~~~~~~~~~~~~~~~
用 Oriented Bounding Box (OBB) 估算 .obj 零件的长宽高
-------------------------------------------------------
依赖: trimesh  (pip install trimesh==4.3.*)
"""

from typing import List
import numpy as np
import trimesh
from src.data_formats import PartData, FeatureData


def measure_lwh_obb(obj_path: str, sort: bool = True):
    """
    读取 .obj 文件并计算 OBB 的外接尺寸.

    Parameters
    ----------
    obj_path : str
        OBJ 文件路径
    sort : bool, default True
        True  → 返回值按长度≥宽度≥高度排序  
        False → 返回值顺序与 trimesh 计算出的 OBB 主轴一致

    Returns
    -------
    tuple[float, float, float]
        (length, width, height)，单位随模型本身一致
    """
    # 1. 读 mesh，process=False 保留作者原始坐标
    mesh = trimesh.load(obj_path, process=False)

    if not isinstance(mesh, trimesh.Trimesh):
        # 多子对象时，trimesh 返回 Scene；合并成单一网格
        mesh = trimesh.util.concatenate(mesh.dump())

    # 2. 获取 "最小体积" 的定向包围盒
    obb = mesh.bounding_box_oriented

    # 3. extents: OBB 在其三个主轴方向上的边长
    extents = (
        obb.primitive.extents
        if hasattr(obb, "primitive")
        else obb.extents  # trimesh <4.2 回退
    )
    if sort:
        extents = np.sort(extents)[::-1]

    return tuple(float(x) for x in extents)


def analyze_holes(features: List[FeatureData]):
    """
    统计零件中的孔特征数量、直径均值/方差、深度均值/方差。
    目前假设所有features都是孔。

    参数：
        part (PartData): 零件数据
    返回：
        (hole_count, diameter_mean, diameter_std, depth_mean, depth_std)
    """
    holes = features  # 不再筛选，全部视为孔
    hole_count = len(holes)
    if hole_count == 0:
        return 0, 0.0, 0.0, 0.0, 0.0
    diameters = [f.diameter for f in holes]
    depths = [f.length for f in holes]
    diameter_mean = float(np.mean(diameters))
    diameter_std = float(np.std(diameters))
    depth_mean = float(np.mean(depths))
    depth_std = float(np.std(depths))
    return hole_count, diameter_mean, diameter_std, depth_mean, depth_std


# === demo ===
if __name__ == "__main__":
    length, width, height = measure_lwh_obb(r"test\41032_ed481084\8f8e0de4-05df-11ec-b2a4-026936bafcd9.obj")
    print(f"OBB 尺寸: 长={length:.3f}, 宽={width:.3f}, 高={height:.3f}")

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导出Neo4j数据库的schema为结构化报告（Markdown格式）
"""

import os
import sys
import argparse
import logging
from datetime import datetime
from neo4j import GraphDatabase

# 添加项目根目录到Python路径，以便导入src模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入配置
from src.config import Config

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# HTML报表模板
HTML_TEMPLATE = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neo4j数据库Schema报告</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 { 
            color: #205081; 
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
        }
        h2 { 
            color: #205081; 
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        h3 { 
            color: #3b73af; 
            margin-top: 20px;
        }
        table { 
            border-collapse: collapse; 
            width: 100%;
            margin: 20px 0;
        }
        th, td { 
            text-align: left; 
            padding: 12px; 
            border: 1px solid #ddd;
        }
        th { 
            background-color: #f2f2f2; 
            color: #205081;
        }
        tr:nth-child(even) { 
            background-color: #f9f9f9; 
        }
        .summary-card {
            background-color: #f8f8f8;
            border-left: 4px solid #3b73af;
            padding: 15px;
            margin: 20px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .node-label {
            background-color: #e7f4ff;
            border-radius: 3px;
            padding: 2px 5px;
            margin-right: 5px;
        }
        .relationship-type {
            background-color: #fff0e7;
            border-radius: 3px;
            padding: 2px 5px;
            margin-right: 5px;
        }
        .timestamp {
            color: #666;
            font-style: italic;
            margin-top: -10px;
        }
        .property-list {
            list-style-type: none;
            padding-left: 10px;
            display: flex;
            flex-wrap: wrap;
        }
        .property-item {
            background-color: #f5f5f5;
            border-radius: 3px;
            padding: 2px 6px;
            margin: 2px 5px 2px 0;
            font-family: monospace;
        }
        .pattern-list {
            list-style-type: none;
            padding-left: 0;
        }
        .pattern-item {
            font-family: monospace;
            padding: 5px;
            margin: 5px 0;
            background-color: #f5f5f5;
            border-radius: 3px;
        }
        .stats {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 15px;
            margin: 10px 0;
            flex: 0 0 calc(33% - 20px);
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #205081;
        }
        .stat-label {
            color: #666;
        }
    </style>
</head>
<body>
    <h1>Neo4j数据库Schema报告</h1>
    <p class="timestamp">生成时间: {timestamp}</p>
    
    <div class="summary-card">
        <h2>数据库概览</h2>
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value">{total_node_labels}</div>
                <div class="stat-label">节点标签</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{total_relationship_types}</div>
                <div class="stat-label">关系类型</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{total_nodes}</div>
                <div class="stat-label">节点数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{total_relationships}</div>
                <div class="stat-label">关系数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{total_indexes}</div>
                <div class="stat-label">索引数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{total_constraints}</div>
                <div class="stat-label">约束数量</div>
            </div>
        </div>
    </div>
    
    <h2>1. 节点标签及其属性</h2>
    {node_labels_html}
    
    <h2>2. 关系类型及其属性</h2>
    {relationship_types_html}
    
    <h2>3. 索引信息</h2>
    {indexes_html}
    
    <h2>4. 约束信息</h2>
    {constraints_html}
</body>
</html>
"""

class Neo4jSchemaReporter:
    def __init__(self, uri, user, password):
        """初始化Neo4j连接"""
        self.driver = GraphDatabase.driver(uri, auth=(user, password))

    def close(self):
        """关闭Neo4j连接"""
        self.driver.close()
        
    def _safe_run(self, session, query, message=None):
        """安全执行Cypher查询，捕获可能的异常"""
        try:
            result = list(session.run(query))
            return result
        except Exception as e:
            logger.warning(f"{message if message else '查询执行失败'}: {str(e)}")
            return []

    def get_node_labels(self, session):
        """获取所有节点标签"""
        logger.info("正在获取所有节点标签...")
        result = self._safe_run(
            session,
            "CALL db.labels() YIELD label RETURN label",
            "获取节点标签失败"
        )
        return [record["label"] for record in result]
        
    def get_node_properties(self, session, label):
        """获取指定标签节点的所有属性"""
        logger.info(f"正在获取节点标签 {label} 的属性...")
        result = self._safe_run(
            session,
            f"""
            MATCH (n:{label})
            WITH keys(n) AS props
            UNWIND props AS prop
            RETURN DISTINCT prop
            LIMIT 100
            """,
            f"获取节点 {label} 的属性失败"
        )
        return [record["prop"] for record in result]
        
    def get_relationship_types(self, session):
        """获取所有关系类型"""
        logger.info("正在获取所有关系类型...")
        result = self._safe_run(
            session,
            "CALL db.relationshipTypes() YIELD relationshipType RETURN relationshipType",
            "获取关系类型失败"
        )
        return [record["relationshipType"] for record in result]
        
    def get_relationship_properties(self, session, rel_type):
        """获取指定关系类型的所有属性"""
        logger.info(f"正在获取关系类型 {rel_type} 的属性...")
        result = self._safe_run(
            session,
            f"""
            MATCH ()-[r:{rel_type}]->()
            WITH keys(r) AS props
            UNWIND props AS prop
            RETURN DISTINCT prop
            LIMIT 100
            """,
            f"获取关系 {rel_type} 的属性失败"
        )
        return [record["prop"] for record in result]
        
    def get_relationship_patterns(self, session):
        """获取关系模式（源节点-关系-目标节点）"""
        logger.info("正在获取关系模式...")
        result = self._safe_run(
            session,
            """
            MATCH (start)-[rel]->(end)
            WITH labels(start) AS sourceLabels, type(rel) AS relType, labels(end) AS targetLabels, count(*) AS relCount
            UNWIND sourceLabels AS sourceLabel
            UNWIND targetLabels AS targetLabel
            RETURN DISTINCT sourceLabel as sourceNodeType, 
                   relType as relationshipType, 
                   targetLabel as targetNodeType,
                   relCount as count
            """,
            "获取关系模式失败"
        )
        
        patterns = []
        for record in result:
            patterns.append({
                "sourceNodeType": record["sourceNodeType"],
                "relationshipType": record["relationshipType"],
                "targetNodeType": record["targetNodeType"],
                "count": record["count"]
            })
        return patterns
        
    def get_indexes(self, session):
        """获取索引信息"""
        logger.info("正在获取索引信息...")
        result = self._safe_run(
            session,
            """
            SHOW INDEXES
            YIELD name, type, labelsOrTypes, properties, options
            RETURN name, type, labelsOrTypes, properties, options
            """,
            "获取索引信息失败"
        )
        
        indexes = []
        for record in result:
            indexes.append({
                "name": record["name"],
                "type": record["type"],
                "labelsOrTypes": record["labelsOrTypes"],
                "properties": record["properties"],
                "options": record.get("options", {})
            })
        return indexes
        
    def get_constraints(self, session):
        """获取约束信息"""
        logger.info("正在获取约束信息...")
        result = self._safe_run(
            session,
            """
            SHOW CONSTRAINTS
            YIELD name, type, labelsOrTypes, properties, options
            RETURN name, type, labelsOrTypes, properties, options
            """,
            "获取约束信息失败"
        )
        
        constraints = []
        for record in result:
            constraints.append({
                "name": record["name"],
                "type": record["type"],
                "labelsOrTypes": record["labelsOrTypes"],
                "properties": record["properties"],
                "options": record.get("options", {})
            })
        return constraints
    
    def get_node_counts(self, session):
        """获取节点数量统计"""
        logger.info("正在获取节点数量统计...")
        result = self._safe_run(
            session,
            """
            MATCH (n)
            WITH labels(n) AS nodeLabels
            UNWIND nodeLabels AS label
            RETURN label, count(label) as count
            """,
            "获取节点统计失败"
        )
        
        counts = {}
        for record in result:
            counts[record["label"]] = record["count"]
        return counts
        
    def get_relationship_counts(self, session):
        """获取关系数量统计"""
        logger.info("正在获取关系数量统计...")
        result = self._safe_run(
            session,
            """
            MATCH ()-[r]->()
            RETURN type(r) AS relType, count(r) AS count
            """,
            "获取关系统计失败"
        )
        
        counts = {}
        for record in result:
            counts[record["relType"]] = record["count"]
        return counts
    
    def generate_markdown_report(self):
        """生成Markdown格式的结构化报告"""
        with self.driver.session() as session:
            node_labels = self.get_node_labels(session)
            rel_types = self.get_relationship_types(session)
            indexes = self.get_indexes(session)
            constraints = self.get_constraints(session)
            rel_patterns = self.get_relationship_patterns(session)
            node_counts = self.get_node_counts(session)
            rel_counts = self.get_relationship_counts(session)
            
            # 构建Markdown报告
            md_report = []
            
            # 标题
            md_report.append("# Neo4j数据库Schema报告")
            md_report.append(f"\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            # 1. 节点标签及其属性
            md_report.append("## 1. 节点标签及其属性")
            if node_labels:
                for label in sorted(node_labels):
                    count = node_counts.get(label, "未知")
                    md_report.append(f"\n### 标签: {label} (数量: {count})")
                    
                    properties = self.get_node_properties(session, label)
                    if properties:
                        md_report.append("属性列表:")
                        for prop in sorted(properties):
                            md_report.append(f"- {prop}")
                    else:
                        md_report.append("- 无属性或数据不足")
            else:
                md_report.append("\n未发现任何节点标签。")
            
            # 2. 关系类型及其属性
            md_report.append("\n## 2. 关系类型及其属性")
            if rel_types:
                for rel_type in sorted(rel_types):
                    count = rel_counts.get(rel_type, "未知")
                    md_report.append(f"\n### 关系类型: {rel_type} (数量: {count})")
                    
                    properties = self.get_relationship_properties(session, rel_type)
                    if properties:
                        md_report.append("属性列表:")
                        for prop in sorted(properties):
                            md_report.append(f"- {prop}")
                    else:
                        md_report.append("- 无属性或数据不足")
                    
                    # 添加此关系类型的模式
                    related_patterns = [p for p in rel_patterns if p["relationshipType"] == rel_type]
                    if related_patterns:
                        md_report.append("\n存在的关系模式:")
                        for pattern in related_patterns:
                            md_report.append(f"- ({pattern['sourceNodeType']})-[:{pattern['relationshipType']}]->({pattern['targetNodeType']}) (数量: {pattern['count']})")
            else:
                md_report.append("\n未发现任何关系类型。")
            
            # 3. 索引信息
            md_report.append("\n## 3. 索引信息")
            if indexes:
                md_report.append("\n| 名称 | 类型 | 标签/关系类型 | 属性 |")
                md_report.append("| ---- | ---- | ------------ | ---- |")
                
                for idx in indexes:
                    name = idx["name"]
                    idx_type = idx["type"]
                    # 处理labelsOrTypes可能是字符串或列表的情况
                    labels_or_types = idx["labelsOrTypes"]
                    if isinstance(labels_or_types, list):
                        labels = ", ".join(labels_or_types)
                    else:
                        labels = str(labels_or_types)
                    
                    # 处理properties可能是字符串或列表的情况
                    props_list = idx["properties"]
                    if isinstance(props_list, list):
                        props = ", ".join(props_list)
                    else:
                        props = str(props_list)
                    
                    md_report.append(f"| {name} | {idx_type} | {labels} | {props} |")
            else:
                md_report.append("\n未发现任何索引。")
            
            # 4. 约束信息
            md_report.append("\n## 4. 约束信息")
            if constraints:
                md_report.append("\n| 名称 | 类型 | 标签/关系类型 | 属性 |")
                md_report.append("| ---- | ---- | ------------ | ---- |")
                
                for constraint in constraints:
                    name = constraint["name"]
                    con_type = constraint["type"]
                    # 处理labelsOrTypes可能是字符串或列表的情况
                    labels_or_types = constraint["labelsOrTypes"]
                    if isinstance(labels_or_types, list):
                        labels = ", ".join(labels_or_types)
                    else:
                        labels = str(labels_or_types)
                    
                    # 处理properties可能是字符串或列表的情况
                    props_list = constraint["properties"]
                    if isinstance(props_list, list):
                        props = ", ".join(props_list)
                    else:
                        props = str(props_list)
                    
                    md_report.append(f"| {name} | {con_type} | {labels} | {props} |")
            else:
                md_report.append("\n未发现任何约束。")
                
            # 5. 统计信息摘要
            md_report.append("\n## 5. 数据库统计摘要")
            md_report.append(f"\n- 总节点标签数: {len(node_labels)}")
            md_report.append(f"- 总关系类型数: {len(rel_types)}")
            md_report.append(f"- 总索引数: {len(indexes)}")
            md_report.append(f"- 总约束数: {len(constraints)}")
            
            total_nodes = sum(node_counts.values()) if node_counts else 0
            total_rels = sum(rel_counts.values()) if rel_counts else 0
            md_report.append(f"- 总节点数: {total_nodes}")
            md_report.append(f"- 总关系数: {total_rels}")
            
            return "\n".join(md_report)
            
    def generate_html_report(self):
        """生成HTML格式的结构化报告"""
        with self.driver.session() as session:
            node_labels = self.get_node_labels(session)
            rel_types = self.get_relationship_types(session)
            indexes = self.get_indexes(session)
            constraints = self.get_constraints(session)
            rel_patterns = self.get_relationship_patterns(session)
            node_counts = self.get_node_counts(session)
            rel_counts = self.get_relationship_counts(session)
            
            # 生成节点标签HTML
            node_labels_html = ""
            for label in sorted(node_labels):
                count = node_counts.get(label, "未知")
                node_labels_html += f"""
                <h3><span class="node-label">{label}</span> (数量: {count})</h3>
                """
                
                properties = self.get_node_properties(session, label)
                if properties:
                    node_labels_html += '<div class="property-list">'
                    for prop in sorted(properties):
                        node_labels_html += f'<span class="property-item">{prop}</span>'
                    node_labels_html += '</div>'
                else:
                    node_labels_html += "<p>无属性或数据不足</p>"
                    
            # 生成关系类型HTML
            relationship_types_html = ""
            for rel_type in sorted(rel_types):
                count = rel_counts.get(rel_type, "未知")
                relationship_types_html += f"""
                <h3><span class="relationship-type">{rel_type}</span> (数量: {count})</h3>
                """
                
                properties = self.get_relationship_properties(session, rel_type)
                if properties:
                    relationship_types_html += '<div class="property-list">'
                    for prop in sorted(properties):
                        relationship_types_html += f'<span class="property-item">{prop}</span>'
                    relationship_types_html += '</div>'
                else:
                    relationship_types_html += "<p>无属性或数据不足</p>"
                
                # 添加此关系类型的模式
                related_patterns = [p for p in rel_patterns if p["relationshipType"] == rel_type]
                if related_patterns:
                    relationship_types_html += "<p>存在的关系模式:</p>"
                    relationship_types_html += '<ul class="pattern-list">'
                    for pattern in related_patterns:
                        relationship_types_html += f"""
                        <li class="pattern-item">
                            ({pattern['sourceNodeType']})-[:{pattern['relationshipType']}]->({pattern['targetNodeType']}) (数量: {pattern['count']})
                        </li>
                        """
                    relationship_types_html += '</ul>'
            
            # 生成索引HTML
            if indexes:
                indexes_html = """
                <table>
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>类型</th>
                            <th>标签/关系类型</th>
                            <th>属性</th>
                        </tr>
                    </thead>
                    <tbody>
                """
                
                for idx in indexes:
                    name = idx["name"]
                    idx_type = idx["type"]
                    
                    # 处理labelsOrTypes可能是字符串或列表的情况
                    labels_or_types = idx["labelsOrTypes"]
                    if isinstance(labels_or_types, list):
                        labels = ", ".join(labels_or_types)
                    else:
                        labels = str(labels_or_types)
                    
                    # 处理properties可能是字符串或列表的情况
                    props_list = idx["properties"]
                    if isinstance(props_list, list):
                        props = ", ".join(props_list)
                    else:
                        props = str(props_list)
                    
                    indexes_html += f"""
                    <tr>
                        <td>{name}</td>
                        <td>{idx_type}</td>
                        <td>{labels}</td>
                        <td>{props}</td>
                    </tr>
                    """
                
                indexes_html += """
                    </tbody>
                </table>
                """
            else:
                indexes_html = "<p>未发现任何索引。</p>"
            
            # 生成约束HTML
            if constraints:
                constraints_html = """
                <table>
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>类型</th>
                            <th>标签/关系类型</th>
                            <th>属性</th>
                        </tr>
                    </thead>
                    <tbody>
                """
                
                for constraint in constraints:
                    name = constraint["name"]
                    con_type = constraint["type"]
                    
                    # 处理labelsOrTypes可能是字符串或列表的情况
                    labels_or_types = constraint["labelsOrTypes"]
                    if isinstance(labels_or_types, list):
                        labels = ", ".join(labels_or_types)
                    else:
                        labels = str(labels_or_types)
                    
                    # 处理properties可能是字符串或列表的情况
                    props_list = constraint["properties"]
                    if isinstance(props_list, list):
                        props = ", ".join(props_list)
                    else:
                        props = str(props_list)
                    
                    constraints_html += f"""
                    <tr>
                        <td>{name}</td>
                        <td>{con_type}</td>
                        <td>{labels}</td>
                        <td>{props}</td>
                    </tr>
                    """
                
                constraints_html += """
                    </tbody>
                </table>
                """
            else:
                constraints_html = "<p>未发现任何约束。</p>"
                
            # 统计摘要信息
            total_nodes = sum(node_counts.values()) if node_counts else 0
            total_relationships = sum(rel_counts.values()) if rel_counts else 0
            
            # 填充HTML模板
            html_report = HTML_TEMPLATE.format(
                timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                total_node_labels=len(node_labels),
                total_relationship_types=len(rel_types),
                total_nodes=total_nodes,
                total_relationships=total_relationships,
                total_indexes=len(indexes),
                total_constraints=len(constraints),
                node_labels_html=node_labels_html,
                relationship_types_html=relationship_types_html,
                indexes_html=indexes_html,
                constraints_html=constraints_html
            )
            
            return html_report
    
    def generate_summary(self):
        """生成数据库概览摘要"""
        with self.driver.session() as session:
            node_labels = self.get_node_labels(session)
            rel_types = self.get_relationship_types(session)
            indexes = self.get_indexes(session)
            constraints = self.get_constraints(session)
            node_counts = self.get_node_counts(session)
            rel_counts = self.get_relationship_counts(session)
            
            total_nodes = sum(node_counts.values()) if node_counts else 0
            total_rels = sum(rel_counts.values()) if rel_counts else 0
            
            # 按数量排序获取前3个节点标签
            top_node_labels = sorted(
                [(label, count) for label, count in node_counts.items()],
                key=lambda x: x[1],
                reverse=True
            )[:3]
            
            # 按数量排序获取前3个关系类型
            top_rel_types = sorted(
                [(rel_type, count) for rel_type, count in rel_counts.items()],
                key=lambda x: x[1],
                reverse=True
            )[:3]
            
            # 构建摘要
            summary = []
            summary.append("# Neo4j数据库概览摘要")
            summary.append(f"\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            summary.append("\n## 基本统计")
            summary.append(f"- 总节点数: {total_nodes}")
            summary.append(f"- 总关系数: {total_rels}")
            summary.append(f"- 节点标签数: {len(node_labels)}")
            summary.append(f"- 关系类型数: {len(rel_types)}")
            summary.append(f"- 索引数: {len(indexes)}")
            summary.append(f"- 约束数: {len(constraints)}")
            
            summary.append("\n## 主要节点标签")
            for label, count in top_node_labels:
                percent = (count / total_nodes * 100) if total_nodes > 0 else 0
                summary.append(f"- {label}: {count} 节点 ({percent:.1f}%)")
                
            summary.append("\n## 主要关系类型")
            for rel_type, count in top_rel_types:
                percent = (count / total_rels * 100) if total_rels > 0 else 0
                summary.append(f"- {rel_type}: {count} 关系 ({percent:.1f}%)")
                
            if indexes:
                summary.append("\n## 索引")
                for idx in indexes[:5]:  # 最多显示5个索引
                    name = idx["name"]
                    idx_type = idx["type"]
                    summary.append(f"- {name} ({idx_type})")
            
            return "\n".join(summary)
    
    def export_report(self, output_file=None, format="markdown"):
        """导出结构化报告到文件
        
        参数:
            output_file: 输出文件路径，如果为None则使用默认命名
            format: 报告格式，可选值为"markdown"、"html"或"summary"
        """
        if format.lower() not in ["markdown", "html", "summary"]:
            format = "markdown"  # 默认格式
            
        logger.info(f"正在生成Neo4j数据库schema报告 (格式: {format})...")
        
        if format.lower() == "markdown":
            report = self.generate_markdown_report()
            ext = "md"
        elif format.lower() == "html":
            report = self.generate_html_report()
            ext = "html"
        else:  # summary
            report = self.generate_summary()
            ext = "md"
            
        if output_file is None:
            # 默认输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"neo4j_schema_report_{timestamp}.{ext}"
        elif not output_file.endswith(f".{ext}"):
            # 确保文件扩展名正确
            output_file = f"{output_file}.{ext}"
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report)
            
        logger.info(f"Schema报告已导出到: {output_file}")
        return output_file

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='导出Neo4j数据库的schema为结构化报告')
    parser.add_argument('--output', default=None, help='输出文件路径')
    parser.add_argument('--format', choices=['markdown', 'html', 'summary'], 
                        default='markdown', help='输出格式: markdown, html 或 summary')
    
    args = parser.parse_args()
    
    # 从配置中获取Neo4j连接信息
    neo4j_config = Config.get_neo4j_config()
    
    logger.info(f"正在连接到Neo4j数据库: {neo4j_config['uri']}")
    logger.info(f"使用用户名: {neo4j_config['user']}")
    
    reporter = Neo4jSchemaReporter(
        uri=neo4j_config['uri'],
        user=neo4j_config['user'], 
        password=neo4j_config['password']
    )
    
    try:
        output_file = reporter.export_report(args.output, args.format)
        logger.info(f"Schema报告导出成功: {output_file}")
    finally:
        reporter.close()

if __name__ == "__main__":
    main() 
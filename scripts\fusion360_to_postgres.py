import json
import logging
import os
import sys
import psycopg2
from psycopg2.extras import execute_values
from typing import Dict, Any, List

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data_formats import CADModelData
from src.fusion360_extractor import Fusion360Extractor
from src.utils.file_utils import get_fusion360_test_paths
from src.config import Config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class PostgresCADImporter:
    def __init__(self, db_params: Dict[str, str]):
        """
        初始化PostgreSQL导入器
        
        Args:
            db_params: 数据库连接参数
        """
        self.db_params = db_params
        self.conn = None
        self.cursor = None
        
    def connect(self):
        """建立数据库连接"""
        try:
            self.conn = psycopg2.connect(**self.db_params)
            self.cursor = self.conn.cursor()
            logging.info("成功连接到PostgreSQL数据库")
        except Exception as e:
            logging.error(f"连接数据库失败: {e}")
            raise
            
    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
            logging.info("数据库连接已关闭")
            
    def import_part(self, part_data: Dict[str, Any], top_level_assembly_id: str) -> str:
        """
        导入单个零件到数据库
        
        Args:
            part_data: 零件数据字典
            top_level_assembly_id: 顶层装配体ID
            
        Returns:
            str: 零件ID
        """
        # 准备插入数据
        columns = [
            'uuid', 'top_level_assembly_id', 'name', 'material', 'description',
            'length', 'width', 'height', 'area', 'volume', 'density', 'mass',
            'hole_count', 'hole_diameter_mean', 'hole_diameter_std',
            'hole_depth_mean', 'hole_depth_std'
        ]

        def round7(val):
            if isinstance(val, float):
                return round(val, 7)
            return val
        values = [
            part_data.get('part_id'),  # uuid
            top_level_assembly_id,     # top_level_assembly_id
            part_data.get('name'),     # name
            part_data.get('material'), # material
            part_data.get('description'),  # description
            round7(part_data.get('length', 0.0)),   # length
            round7(part_data.get('width', 0.0)),    # width
            round7(part_data.get('height', 0.0)),   # height
            round7(part_data.get('area', 0.0)),    # area
            round7(part_data.get('volume', 0.0)),  # volume
            round7(part_data.get('density', 0.0)), # density
            round7(part_data.get('mass', 0.0)),    # mass
            part_data.get('hole_count', 0.0),     # hole_count
            round7(part_data.get('hole_diameter_mean', 0.0)), # hole_diameter_mean
            round7(part_data.get('hole_diameter_std', 0.0)), # hole_diameter_std
            round7(part_data.get('hole_depth_mean', 0.0)),   # hole_depth_mean
            round7(part_data.get('hole_depth_std', 0.0) ) # hole_depth_std
        ]
        
        # 构建INSERT语句
        insert_query = f"""
            INSERT INTO cad_rag.parts ({', '.join(columns)})
            VALUES ({', '.join(['%s'] * len(columns))})
            ON CONFLICT (uuid) DO UPDATE SET
            {', '.join(f"{col} = EXCLUDED.{col}" for col in columns if col != 'uuid')}
            RETURNING uuid;
        """
        
        try:
            self.cursor.execute(insert_query, values)
            part_id = self.cursor.fetchone()[0]
            self.conn.commit()
            logging.info(f"成功导入零件: {part_data['name']} (ID: {part_id})")
            return part_id
        except Exception as e:
            self.conn.rollback()
            logging.error(f"导入零件失败: {e}")
            raise

def import_from_fusion360(file_path: str, importer: PostgresCADImporter) -> Dict[str, Any]:
    """
    从Fusion360 JSON文件导入到PostgreSQL数据库
    
    Args:
        file_path: Fusion360 JSON文件路径
        importer: PostgreSQL导入器实例
        
    Returns:
        Dict[str, Any]: 导入结果统计
    """
    # 创建Fusion360提取器
    extractor = Fusion360Extractor(file_path, extract_shape_embedding=False)
    
    # 从文件提取数据
    cad_data = extractor.convert()
    
    # 打印装配层次结构树
    cad_data.print_assembly_tree()
    
    # 获取顶层装配体ID
    top_level_assembly_id = cad_data.assembly.assembly_id
    
    # 导入所有零件
    imported_parts = []
    
    # 处理顶层装配体中的零件
    for part in cad_data.assembly.parts:
        part_dict = {
            'part_id': part.part_id,
            'name': part.name,
            'area': part.area,
            'volume': part.volume,
            'density': part.density,
            'mass': part.mass,
            'material': part.material,
            'description': getattr(part, 'description', None),
            'length': getattr(part, 'length', 0.0),
            'width': getattr(part, 'width', 0.0),
            'height': getattr(part, 'height', 0.0),
            'hole_count': getattr(part, 'hole_count', 0.0),
            'hole_diameter_mean': getattr(part, 'hole_diameter_mean', 0.0),
            'hole_diameter_std': getattr(part, 'hole_diameter_std', 0.0),
            'hole_depth_mean': getattr(part, 'hole_depth_mean', 0.0),
            'hole_depth_std': getattr(part, 'hole_depth_std', 0.0),
            'properties': part.properties
        }
        imported_parts.append(importer.import_part(part_dict, top_level_assembly_id))
    
    # 递归处理子装配体中的零件
    def process_subassembly(subassembly):
        for part in subassembly.parts:
            part_dict = {
                'part_id': part.part_id,
                'name': part.name,
                'area': part.area,
                'volume': part.volume,
                'density': part.density,
                'mass': part.mass,
                'material': part.material,
                'description': getattr(part, 'description', None),
                'length': getattr(part, 'length', 0.0),
                'width': getattr(part, 'width', 0.0),
                'height': getattr(part, 'height', 0.0),
                'hole_count': getattr(part, 'hole_count', 0.0),
                'hole_diameter_mean': getattr(part, 'hole_diameter_mean', 0.0),
                'hole_diameter_std': getattr(part, 'hole_diameter_std', 0.0),
                'hole_depth_mean': getattr(part, 'hole_depth_mean', 0.0),
                'hole_depth_std': getattr(part, 'hole_depth_std', 0.0),
                'properties': part.properties
            }
            imported_parts.append(importer.import_part(part_dict, top_level_assembly_id))
        
        for sub in subassembly.subassemblies:
            process_subassembly(sub)
    
    # 处理所有子装配体
    for subassembly in cad_data.assembly.subassemblies:
        process_subassembly(subassembly)
    
    return {
        'assembly_id': top_level_assembly_id,
        'assembly_name': cad_data.assembly.name,
        'imported_parts': imported_parts
    }

def batch_import_from_fusion360(root_dir: str, importer: PostgresCADImporter) -> Dict[str, Dict[str, Any]]:
    """
    批量从指定根目录下的所有子文件夹中的assembly.json导入到数据库
    
    Args:
        root_dir: 根目录路径，每个子文件夹下应有assembly.json
        importer: PostgreSQL导入器实例
    
    Returns:
        Dict[str, Dict[str, Any]]: 导入结果统计
    """
    imported = {}
    for dirpath, dirnames, filenames in os.walk(root_dir):
        if 'assembly.json' in filenames:
            assembly_path = os.path.join(dirpath, 'assembly.json')
            try:
                result = import_from_fusion360(assembly_path, importer)
                imported[dirpath] = result
            except Exception as e:
                logging.error(f"导入{assembly_path}失败: {e}")
                imported[dirpath] = {'error': str(e)}
    return imported

if __name__ == "__main__":
    # 使用配置文件中的数据库参数
    db_params = Config.POSTGRES_CONFIG
    
    # 创建导入器实例
    importer = PostgresCADImporter(db_params)
    
    try:
        # 连接数据库
        importer.connect()
        
        # 使用测试集路径进行导入
        test_paths = get_fusion360_test_paths()
        print(f"发现 {len(test_paths)} 个测试集文件夹")
        
        test_results = {}
        for test_path in test_paths:
            assembly_json = os.path.join(test_path, "assembly.json")
            if os.path.exists(assembly_json):
                print(f"正在导入: {assembly_json}")
                try:
                    result = import_from_fusion360(assembly_json, importer)
                    test_results[test_path] = result
                except Exception as e:
                    logging.error(f"导入{assembly_json}失败: {e}")
                    test_results[test_path] = {'error': str(e)}
            else:
                print(f"警告: assembly.json不存在于路径: {test_path}")
        
        print("测试集导入结果:")
        print(json.dumps(test_results, indent=2, ensure_ascii=False))
        
    finally:
        # 关闭数据库连接
        importer.close() 
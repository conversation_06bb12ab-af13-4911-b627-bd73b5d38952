import argparse
import torch
import os
import sys
# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.shape_description.clip import CLIPFeatureExtractor
from scripts.shape_embedding_index import ShapeEmbeddingIndex

from PIL import Image
import numpy as np
from src.knowledge_graph import CADKnowledgeGraph
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ImageSimilaritySearch:
    """
    基于图像相似度的CAD模型检索类
    
    使用CLIP模型提取图像特征，通过向量索引计算与数据库中节点的相似度
    """
    
    def __init__(self, clip_extractor):
        """
        初始化搜索类
        
        Args:
            clip_model_name: CLIP模型名称
        """
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logging.info(f"使用设备: {self.device}")
        
        # 加载CLIP模型
        self.clip_extractor = clip_extractor
        
        # 初始化形状嵌入索引
        self.shape_index = ShapeEmbeddingIndex()
        
    def extract_image_embedding(self, image_path):
        """
        提取图像的CLIP嵌入向量
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            numpy.ndarray: 图像的CLIP嵌入向量
        """
        try:
            # 加载并预处理图像
            image = self.clip_extractor.preprocess(Image.open(image_path)).unsqueeze(0)
            
            # 提取特征
            with torch.no_grad():
                embedding = self.clip_extractor(image)
                
            # 归一化并转换为numpy数组
            return embedding.squeeze(0).cpu().numpy()
            
        except Exception as e:
            logging.error(f"提取图像特征失败: {e}")
            raise
            
    def search_similar_nodes(self, image_embedding, top_k=5, similarity_threshold=0.5):
        """
        在知识图谱中搜索与图像最相似的节点
        
        Args:
            image_embedding: 图像嵌入向量
            top_k: 返回的最相似结果数量
            similarity_threshold: 相似度阈值
            
        Returns:
            list: 最相似节点列表，包含节点信息和相似度分数
        """
        # 使用ShapeEmbeddingIndex的向量索引搜索
        try:
            return self.shape_index.search_similar_nodes(
                image_embedding,
                top_k=top_k,
                similarity_threshold=similarity_threshold
            )
        except Exception as e:
            logging.error(f"向量索引搜索失败: {e}")
            
            # 回退到传统的余弦相似度计算方法
            logging.info("尝试使用传统余弦相似度计算方法...")
            
            # Neo4j Cypher查询，计算余弦相似度并返回top_k个结果
            query = """
            MATCH (n)
            WHERE n.shape_embedding IS NOT NULL
            WITH n, gds.similarity.cosine($image_embedding, n.shape_embedding) AS similarity
            WHERE similarity > $threshold
            RETURN n.id AS id, n.name AS name, similarity
            ORDER BY similarity DESC
            LIMIT $top_k
            """
            
            params = {
                "image_embedding": image_embedding.tolist(),
                "top_k": top_k,
                "threshold": similarity_threshold
            }
            
            # 执行查询
            with self.shape_index.kg.driver.session() as session:
                result = session.run(query, params)
                similar_nodes = [dict(record) for record in result]
                
            return similar_nodes
    
    def close(self):
        """释放资源"""
        if self.shape_index:
            self.shape_index.close()
        
def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="基于图像相似度的CAD模型检索")
    parser.add_argument("--image", "-i", type=str, default=r"test\41757_c1173a7e\assembly.png", help="输入图像路径")
    parser.add_argument("--top_k", "-k", type=int, default=5, help="返回的最相似结果数量")
    parser.add_argument("--threshold", "-t", type=float, default=0.5, help="相似度阈值（0-1之间）")
    parser.add_argument("--node_types", "-n", type=str, default='Assembly SubAssembly Part', 
                      help='限制搜索的节点类型，例如: Assembly Part')
    args = parser.parse_args()

    # 创建CLIP特征提取器实例，只初始化一次
    clip_extractor = CLIPFeatureExtractor()

    # 检查图像文件是否存在
    if not os.path.exists(args.image):
        logging.error(f"图像文件不存在: {args.image}")
        return
    
    try:
        # 初始化搜索器
        searcher = ImageSimilaritySearch(clip_extractor)
        
        # 提取图像特征
        logging.info(f"正在提取图像特征: {args.image}")
        image_embedding = searcher.extract_image_embedding(args.image)
        
        # 搜索相似节点
        logging.info("正在搜索相似CAD模型...")
        similar_nodes = searcher.search_similar_nodes(
            image_embedding, 
            args.top_k, 
            args.threshold
        )
        
        # 打印结果
        if similar_nodes:
            logging.info(f"找到 {len(similar_nodes)} 个相似模型:")
            print("-" * 80)
            print(f"{'序号':<6}{'节点类型':<15}{'节点ID':<36}{'名称':<30}{'相似度':<8}")
            print("-" * 80)
            for i, node in enumerate(similar_nodes):
                print(f"{i+1:<6}{node['id']:<36}{node['name']:<30}{node['similarity']:.4f}")
        else:
            logging.info("未找到相似模型")
            
    except Exception as e:
        logging.error(f"搜索失败: {e}")
        
    finally:
        searcher.close()
        
if __name__ == "__main__":
    main() 
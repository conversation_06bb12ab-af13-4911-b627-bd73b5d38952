import os
import pickle
import numpy as np
from pymilvus import connections, Collection, FieldSchema, CollectionSchema
from pymilvus import DataType, utility


class MilvusHandler:
    def __init__(self, host="127.0.0.1", port="19530"):
        """
        初始化Milvus处理器
        
        参数:
            host (str): Milvus服务器主机名
            port (str): Mil<PERSON><PERSON>服务器端口
        """
        self.host = host
        self.port = port
        self._connect()
    
    def _connect(self):
        """连接到Milvus服务器"""
        try:
            connections.connect(host=self.host, port=self.port)
            print(f"成功连接到Milvus服务器 {self.host}:{self.port}")
        except Exception as e:
            print(f"连接Milvus服务器失败: {e}")
            raise
    
    def create_part_collection(self, collection_name="parts", dim=768):
        """
        创建零件集合
        
        参数:
            collection_name (str): 集合名称
            dim (int): 嵌入向量维度
            
        返回:
            Collection: 创建的集合对象
        """
        if utility.has_collection(collection_name):
            print(f"集合 '{collection_name}' 已存在，将被删除并重新创建")
            utility.drop_collection(collection_name)
        
        # 定义集合字段
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="part_id", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="assembly_id", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=dim)
        ]
        
        # 创建集合架构
        schema = CollectionSchema(fields=fields, description="零件形状嵌入集合")
        
        # 创建集合
        collection = Collection(name=collection_name, schema=schema)
        
        # 为向量字段创建索引
        index_params = {
            "metric_type": "L2",
            "index_type": "HNSW",
            "params": {"M": 8, "efConstruction": 200}
        }
        collection.create_index(field_name="embedding", index_params=index_params)
        print(f"已成功创建集合 '{collection_name}' 并添加索引")
        
        return collection
    
    def load_from_pickle_and_insert(self, collection_name="parts", pkl_path="dataset/clip_features.pkl"):
        """
        从pickle文件加载嵌入并插入到Milvus集合
        
        参数:
            collection_name (str): 集合名称
            pkl_path (str): pickle文件路径
        
        返回:
            int: 插入的实体数量
        """
        # 确保集合存在
        if not utility.has_collection(collection_name):
            print(f"集合 '{collection_name}' 不存在，先创建一个")
            # 从第一个嵌入向量获取维度
            with open(pkl_path, 'rb') as f:
                data = pickle.load(f)
                assembly_id = next(iter(data))
                part_id = next(iter(data[assembly_id]))
                dim = data[assembly_id][part_id].shape[1]
            self.create_part_collection(collection_name, dim)
        
        # 加载集合
        collection = Collection(collection_name)
        collection.load()
        
        # 读取pickle文件
        with open(pkl_path, 'rb') as f:
            data = pickle.load(f)
        
        # 准备插入数据
        part_ids = []
        assembly_ids = []
        embeddings = []
        
        for assembly_id, parts in data.items():
            for part_id, embedding in parts.items():
                part_ids.append(part_id)
                assembly_ids.append(assembly_id)
                # 确保是一维向量
                embeddings.append(embedding.reshape(-1).tolist())
        
        # 插入数据
        insert_data = [part_ids, assembly_ids, embeddings]
        collection.insert(insert_data)
        
        # 刷新集合以确保数据可搜索
        collection.flush()
        print(f"已成功插入 {len(part_ids)} 个零件到集合 '{collection_name}'")
        
        return len(part_ids)
    
    def search_similar_parts(self, collection_name, query_embedding, top_k=5):
        """
        搜索与查询向量相似的零件
        
        参数:
            collection_name (str): 集合名称
            query_embedding (np.ndarray): 查询嵌入向量
            top_k (int): 返回的最相似结果数量
            
        返回:
            list: 搜索结果列表
        """
        # 确保集合存在
        if not utility.has_collection(collection_name):
            raise ValueError(f"集合 '{collection_name}' 不存在")
        
        # 加载集合
        collection = Collection(collection_name)
        collection.load()
        
        # 准备查询向量
        query_vec = query_embedding.reshape(-1).tolist()
        
        # 执行搜索
        search_params = {
            "metric_type": "L2",
            "params": {"ef": 100}
        }
        results = collection.search(
            data=[query_vec],
            anns_field="embedding",
            param=search_params,
            limit=top_k,
            output_fields=["part_id", "assembly_id"]
        )
        
        return results


def load_from_clip_features_to_milvus(
    pkl_path="dataset/clip_features.pkl", 
    collection_name="parts", 
    host="127.0.0.1", 
    port="19530"
):
    """
    从CLIP特征文件加载数据并导入到Milvus
    
    参数:
        pkl_path (str): pickle文件路径
        collection_name (str): Milvus集合名称
        host (str): Milvus服务器主机
        port (str): Milvus服务器端口
        
    返回:
        int: 插入的零件数量
    """
    # 创建Milvus处理器
    handler = MilvusHandler(host=host, port=port)
    
    # 加载并插入数据
    inserted_count = handler.load_from_pickle_and_insert(
        collection_name=collection_name,
        pkl_path=pkl_path
    )
    
    return inserted_count

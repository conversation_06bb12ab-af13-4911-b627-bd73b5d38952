import argparse
import os
import sys
import logging
import time

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.knowledge_graph import CADKnowledgeGraph
from src.config import Config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ShapeEmbeddingIndex:
    """
    为Assembly、SubAssembly、Part节点构建向量索引，用于形状相似度检索
    
    这个类提供以下功能:
    1. 创建向量索引
    2. 删除向量索引
    3. 查询相似形状
    """
    
    def __init__(self):
        """
        初始化索引管理器
        """
        self.kg = CADKnowledgeGraph.from_config()
        self.index_name = "shape_embedding_index"
        
    def close(self):
        """关闭数据库连接"""
        if self.kg:
            self.kg.close()
    
    def create_index(self, vector_dimension=768, similarity_metric="cosine"):
        """
        为Assembly、SubAssembly、Part节点创建统一的向量索引
        
        Args:
            vector_dimension: 向量维度
            similarity_metric: 相似度度量方式，可选值: cosine, euclidean
        
        Returns:
            bool: 是否创建成功
        """
        start_time = time.time()
        logging.info(f"开始创建形状向量索引: {self.index_name}")
        
        # 先检查索引是否已存在，如存在则先删除
        self.drop_index()
        
        # 为Assembly、SubAssembly、Part创建统一的向量索引
        # 使用shape_embedding属性作为向量字段
        # 修改API调用，根据Neo4j版本调整参数格式
        query = f"""
            CREATE VECTOR INDEX {self.index_name} IF NOT EXISTS
            FOR (p:Assembly) ON (p.shape_embedding)
            OPTIONS {{
            indexConfig: {{
                `vector.dimensions`: {vector_dimension},
                `vector.similarity_function`: '{similarity_metric}'
            }}
            }}
        """

        try:
            with self.kg.driver.session() as session:
                session.run(query)
                
            end_time = time.time()
            execution_time = end_time - start_time
            logging.info(f"形状向量索引创建成功，耗时: {execution_time:.4f}秒")
            return True
        except Exception as e:
            logging.error(f"创建向量索引失败: {e}")
            return False
    
    def drop_index(self):
        """
        删除向量索引（如果存在）
        
        Returns:
            bool: 是否删除成功
        """
        # 首先检查索引是否存在
        try:
            indexes = self.list_indexes()
            index_exists = any(index['name'] == self.index_name for index in indexes)
            
            if not index_exists:
                logging.info(f"索引 {self.index_name} 不存在，无需删除")
                return True
                
            # 使用通用的索引删除方法
            query = """
            CALL db.index.drop($index_name)
            """
            
            with self.kg.driver.session() as session:
                session.run(query, {"index_name": self.index_name})
            logging.info(f"已删除向量索引: {self.index_name}")
            return True
        except Exception as e:
            logging.warning(f"删除向量索引时发生错误: {e}")
            return False
    
    def list_indexes(self):
        """
        列出所有向量索引
        
        Returns:
            list: 索引列表
        """
        # 使用更通用的索引列表查询
        query = """
        SHOW INDEXES
        """
        
        try:
            with self.kg.driver.session() as session:
                result = session.run(query)
                all_indexes = [dict(record) for record in result]
                
            # 过滤出向量索引
            vector_indexes = []
            for index in all_indexes:
                if 'type' in index and index['type'] == 'VECTOR':
                    vector_indexes.append(index)
                # 兼容不同版本Neo4j的输出格式
                elif 'indexType' in index and 'VECTOR' in str(index['indexType']):
                    vector_indexes.append(index)
                    
            for idx, index in enumerate(vector_indexes):
                logging.info(f"索引 {idx+1}: {index.get('name', index.get('indexName', 'Unknown'))}")
                for k, v in index.items():
                    logging.info(f"  - {k}: {v}")
                
            return vector_indexes
        except Exception as e:
            logging.error(f"获取向量索引列表失败: {e}")
            return []
    
    def search_similar_nodes(self, query_vector, top_k=5, similarity_threshold=0.6):
        """
        搜索与查询向量相似的节点
        
        Args:
            query_vector: 查询向量
            top_k: 返回结果数量限制
            similarity_threshold: 相似度阈值
            node_types: 节点类型过滤，如 ['Assembly', 'Part']
        
        Returns:
            list: 相似节点列表，包含节点信息和相似度分数
        """
        params = {
            "query_vector": query_vector.tolist() if hasattr(query_vector, 'tolist') else query_vector,
            "top_k": top_k,
            "similarity_threshold": similarity_threshold
        }
            
        query = f"""
        CALL db.index.vector.queryNodes(
            '{self.index_name}',
            $top_k,
            $query_vector
        )
        YIELD node, score
        RETURN 
            node.name AS name,
            node.id AS id,
            score AS similarity
        ORDER BY similarity DESC
        """
        
        try:
            with self.kg.driver.session() as session:
                result = session.run(query, params)
                similar_nodes = [dict(record) for record in result]
                
            logging.info(f"找到 {len(similar_nodes)} 个相似节点")
            return similar_nodes
        except Exception as e:
            logging.error(f"搜索相似节点失败: {e}")
            return []
            
def main():
    """命令行入口函数"""
    parser = argparse.ArgumentParser(description="CAD形状向量索引管理工具")
    subparsers = parser.add_subparsers(dest='command', help='子命令')
    
    # 创建索引命令
    create_parser = subparsers.add_parser('create', help='创建形状向量索引')
    create_parser.add_argument('--dimension', '-d', type=int, default=768, help='向量维度')
    create_parser.add_argument('--metric', '-m', type=str, default='cosine', choices=['cosine', 'euclidean'], help='相似度度量方式')
    
    # 删除索引命令
    subparsers.add_parser('drop', help='删除形状向量索引')
    
    # 列出索引命令
    subparsers.add_parser('list', help='列出所有向量索引')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 实例化索引管理器
    index_manager = ShapeEmbeddingIndex()
    
    try:
        # 根据子命令调用相应功能
        if args.command == 'create':
            index_manager.create_index(vector_dimension=args.dimension, similarity_metric=args.metric)
        elif args.command == 'drop':
            index_manager.drop_index()
        elif args.command == 'list':
            index_manager.list_indexes()
        else:
            parser.print_help()
    except Exception as e:
        logging.error(f"操作失败: {e}")
    finally:
        index_manager.close()

if __name__ == "__main__":
    main() 
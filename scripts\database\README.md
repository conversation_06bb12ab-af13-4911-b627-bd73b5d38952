


## 零件表的创建
以下是包含每个字段注释的 SQL 创建语句（以 PostgreSQL 为例，使用 `COMMENT ON` 添加字段注释）：

```sql
CREATE TABLE cad_rag_parts (
    uuid TEXT PRIMARY KEY,  -- 唯一标识符
    top_level_assembly_id TEXT NOT NULL,  -- 顶层装配体的唯一标识符
    name TEXT,  -- 零件名称
    material TEXT,  -- 材质
    description TEXT,  -- 描述信息
    length FLOAT,  -- 长度（单位：毫米或指定单位）
    width FLOAT,  -- 宽度
    height FLOAT,  -- 高度
    area FLOAT,  -- 表面积
    volume FLOAT,  -- 体积
    density FLOAT,  -- 密度
    mass FLOAT,  -- 质量
    hole_count INT,  -- 孔的数量
    hole_avg_diameter FLOAT,  -- 孔的平均直径
    hole_std_dev_diameter FLOAT,  -- 孔直径的标准差
    hole_avg_length FLOAT,  -- 孔的平均长度
    hole_std_dev_length FLOAT  -- 孔长度的标准差
);

-- 字段注释
COMMENT ON COLUMN cad_rag_parts.uuid IS '唯一标识符';
COMMENT ON COLUMN cad_rag_parts.top_level_assembly_id IS '顶层装配体的唯一标识符';
COMMENT ON COLUMN cad_rag_parts.name IS '零件名称';
COMMENT ON COLUMN cad_rag_parts.material IS '材质';
COMMENT ON COLUMN cad_rag_parts.description IS '描述信息';
COMMENT ON COLUMN cad_rag_parts.length IS '长度';
COMMENT ON COLUMN cad_rag_parts.width IS '宽度';
COMMENT ON COLUMN cad_rag_parts.height IS '高度';
COMMENT ON COLUMN cad_rag_parts.area IS '表面积';
COMMENT ON COLUMN cad_rag_parts.volume IS '体积';
COMMENT ON COLUMN cad_rag_parts.density IS '密度';
COMMENT ON COLUMN cad_rag_parts.mass IS '质量';
COMMENT ON COLUMN cad_rag_parts.hole_count IS '孔的数量';
COMMENT ON COLUMN cad_rag_parts.hole_avg_diameter IS '孔的平均直径';
COMMENT ON COLUMN cad_rag_parts.hole_std_dev_diameter IS '孔直径的标准差';
COMMENT ON COLUMN cad_rag_parts.hole_avg_length IS '孔的平均长度';
COMMENT ON COLUMN cad_rag_parts.hole_std_dev_length IS '孔长度的标准差';
```

如果使用的是 MySQL 或 SQLite，注释语法略有不同

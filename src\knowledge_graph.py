from typing import Dict, List, Any, Optional, Tuple, Union
from neo4j import GraphDatabase
import logging
import time
from src.config import Config
from src.data_formats import CADModelData, AssemblyData, SubAssemblyData, PartData, FeatureData
from src.data_converters import get_converter
import numpy as np
class CADKnowledgeGraph:
    """
    CAD知识图谱操作类，用于管理装配体模型的知识图谱
    
    实体类型:
    - Assembly: 顶层装配体  
    - SubAssembly: 子装配体 
    - Part: 零件
    - Feature: 特征
    
    关系类型:
    - hasSubAssembly: 装配体包含子装配体
    - hasPart: 装配体/子装配体包含零件
    - hasFeature: 零件包含特征
    """
    
    def __init__(self, uri: str = None, user: str = None, password: str = None):
        """
        初始化知识图谱连接
        
        Args:
            uri: Neo4j数据库URI，例如"bolt://localhost:7687"，如果为None则使用环境变量
            user: 用户名，如果为None则使用环境变量
            password: 密码，如果为None则使用环境变量
        """
        # 如果参数为None，则使用环境变量中的配置
        self.uri = uri or Config.NEO4J_URI
        self.user = user or Config.NEO4J_USER
        self.password = password or Config.NEO4J_PASSWORD
        
        self.driver = None
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.user, self.password))
            logging.info("成功连接到Neo4j数据库")
        except Exception as e:
            logging.error(f"连接Neo4j数据库失败: {e}")
            raise
    
    @classmethod
    def from_config(cls):
        """
        使用配置创建知识图谱实例
        
        Returns:
            CADKnowledgeGraph实例
        """
        config = Config.get_neo4j_config()
        return cls(
            uri=config["uri"],
            user=config["user"],
            password=config["password"]
        )
    
    def close(self):
        """关闭数据库连接"""
        if self.driver:
            self.driver.close()
            logging.info("已关闭Neo4j数据库连接")
    
    def create_assembly(self, name: str, area: float, volume: float, 
                       density: float, mass: float, industry: str = None, 
                       category: str = None, assembly_id: str = None, shape_embedding: np.ndarray = None) -> str:
        """
        创建顶层装配体
        
        Args:
            name: 装配体名称
            area: 表面积
            volume: 体积
            density: 密度
            mass: 质量
            industry: 行业标签
            category: 类别标签
            assembly_id: 指定的装配体ID，如果不提供则使用Neo4j生成的ID
            
        Returns:
            装配体ID
        """
        start_time = time.time()
        props = {
            "id": assembly_id,
            "name": name,
            "area": area,
            "volume": volume,
            "density": density,
            "mass": mass,
            "shape_embedding": shape_embedding.tolist() if shape_embedding is not None else None
        }
        if industry:
            props["industry"] = industry
        if category:
            props["category"] = category
        
        prop_str = ", ".join([f"{key}: ${key}" for key in props.keys()])
        
        query = f"""
        CREATE (a:Assembly {{{prop_str}}})
        RETURN a.id as id
        """
        
        with self.driver.session() as session:
            result = session.run(query, props)
            record = result.single()
            end_time = time.time()
            execution_time = end_time - start_time
            logging.info(f"创建装配体耗时: {execution_time:.4f}秒")
            return record["id"] if record else None
    
    def create_subassembly(self, name: str, area: float, volume: float, 
                          density: float, mass: float, parent_id: str, 
                          subassembly_id: str = None, shape_embedding: np.ndarray = None) -> str:
        """
        创建子装配体并与父装配体建立关系
        
        Args:
            name: 子装配体名称
            area: 表面积
            volume: 体积
            density: 密度
            mass: 质量
            parent_id: 父装配体ID
            subassembly_id: 指定的子装配体ID，如果不提供则使用Neo4j生成的ID
            
        Returns:
            子装配体ID
        """
        query = """
        MATCH (parent) 
        WHERE parent.id = $parent_id
        CREATE (sub:SubAssembly {
            id: $subassembly_id,
            name: $name,
            area: $area,
            volume: $volume,
            density: $density,
            mass: $mass,
            shape_embedding: $shape_embedding
        })
        CREATE (parent)-[:hasSubAssembly]->(sub)
        RETURN sub.id as id
        """
        
        params = {
            "parent_id": parent_id,
            "subassembly_id": subassembly_id,
            "name": name,
            "area": area,
            "volume": volume,
            "density": density,
            "mass": mass,
            "shape_embedding": shape_embedding.tolist() if shape_embedding is not None else None
        }
        
        with self.driver.session() as session:
            result = session.run(query, params)
            record = result.single()
            return record["id"] if record else None
    
    def create_part(self, name: str, area: float, volume: float, 
                   density: float, mass: float, parent_id: str, 
                   material: str = None, part_id: str = None, shape_embedding: np.ndarray = None) -> str:
        """
        创建零件并与父装配体或子装配体建立关系
        
        Args:
            name: 零件名称
            area: 表面积
            volume: 体积
            density: 密度
            mass: 质量
            parent_id: 父装配体或子装配体ID
            material: 材料
            part_id: 指定的零件ID，如果不提供则使用Neo4j生成的ID
            
        Returns:
            零件ID
        """
        node_props = {
            "id": part_id,
            "name": name,
            "area": area,
            "volume": volume,
            "density": density,
            "mass": mass,
            "shape_embedding": shape_embedding.tolist() if shape_embedding is not None else None
        }
        if material:
            node_props["material"] = material
            
        prop_str = ", ".join([f"{key}: ${key}" for key in node_props.keys()])
        
        query = f"""
        MATCH (parent) 
        WHERE parent.id = $parent_id
        CREATE (p:Part {{{prop_str}}})
        CREATE (parent)-[:hasPart]->(p)
        RETURN p.id as id
        """
        
        params = {"parent_id": parent_id, **node_props}
        
        with self.driver.session() as session:
            result = session.run(query, params)
            record = result.single()
            return record["id"] if record else None
    
    def create_feature(self, length: float, diameter: float, name: str, type: str, parent_id: str, feature_id: str = None) -> str:
        """
        创建特征并与父零件建立关系
        
        Args:
            length: 特征长度
            diameter: 特征直径
            name: 特征名称
            type: 特征类型
            parent_id: 父零件ID
            feature_id: 指定的特征ID，如果不提供则使用Neo4j生成的ID
        
        Returns:
            特征ID
        """
        query = """
        MATCH (parent) 
        WHERE parent.id = $parent_id
        CREATE (f:Feature {
            id: $feature_id,
            length: $length,
            diameter: $diameter,
            name: $name,
            type: $type
        })
        CREATE (parent)-[:hasFeature]->(f)
        RETURN f.id as id
        """
        
        params = {
            "parent_id": parent_id,
            "feature_id": feature_id,
            "length": length,
            "diameter": diameter,
            "name": name,
            "type": type
        }
        
        with self.driver.session() as session:
            result = session.run(query, params)
            record = result.single()
            return record["id"] if record else None
    
    def get_assembly_structure(self, assembly_id: str) -> Dict:
        """
        获取装配体的完整结构
        
        Args:
            assembly_id: 装配体ID
            
        Returns:
            装配体结构的字典表示
        """
        query = """
        MATCH (a:Assembly) WHERE a.id = $assembly_id
        OPTIONAL MATCH (a)-[:hasSubAssembly*1..]->(sub)
        OPTIONAL MATCH (a)-[:hasPart*1..]->(p)
        OPTIONAL MATCH (p)-[:hasFeature]->(f)
        RETURN a, collect(distinct sub) as subassemblies, 
               collect(distinct p) as parts, collect(distinct f) as features
        """
        
        params = {"assembly_id": assembly_id}
        
        with self.driver.session() as session:
            result = session.run(query, params)
            record = result.single()
            if not record:
                return None
                
            assembly = dict(record["a"])
            assembly["subassemblies"] = [dict(sub) for sub in record["subassemblies"]]
            assembly["parts"] = [dict(part) for part in record["parts"]]
            assembly["features"] = [dict(feature) for feature in record["features"]]
            return assembly
    
    def search_by_property(self, property_name: str, property_value: Any) -> List[Dict]:
        """
        根据属性搜索实体
        
        Args:
            property_name: 属性名称
            property_value: 属性值
            
        Returns:
            匹配的实体列表
        """
        query = f"""
        MATCH (n) 
        WHERE n.{property_name} = $property_value
        RETURN n, labels(n) as types
        """
        
        params = {"property_value": property_value}
        
        with self.driver.session() as session:
            result = session.run(query, params)
            return [{"entity": dict(record["n"]), "types": record["types"]} 
                   for record in result]
    
    def get_related_entities(self, entity_id: str, relation_type: str = None) -> List[Dict]:
        """
        获取与指定实体相关的实体
        
        Args:
            entity_id: 实体ID
            relation_type: 关系类型，如果为None则获取所有关系
            
        Returns:
            相关实体列表
        """
        if relation_type:
            query = f"""
            MATCH (n)-[r:{relation_type}]->(related)
            WHERE n.id = $entity_id
            RETURN related, type(r) as relation_type
            """
        else:
            query = """
            MATCH (n)-[r]->(related)
            WHERE n.id = $entity_id
            RETURN related, type(r) as relation_type
            """
        
        params = {"entity_id": entity_id}
        
        with self.driver.session() as session:
            result = session.run(query, params)
            return [{"entity": dict(record["related"]), 
                    "relation": record["relation_type"]} 
                   for record in result]
    
    def delete_entity(self, entity_id: str, cascade: bool = False) -> bool:
        """
        删除实体
        
        Args:
            entity_id: 实体ID
            cascade: 是否级联删除关联实体
            
        Returns:
            是否删除成功
        """
        if cascade:
            query = """
            MATCH (n) 
            WHERE n.id = $entity_id
            OPTIONAL MATCH (n)-[r*1..]->(related)
            DETACH DELETE n, related
            """
        else:
            query = """
            MATCH (n) 
            WHERE n.id = $entity_id
            DETACH DELETE n
            """
        
        params = {"entity_id": entity_id}
        
        with self.driver.session() as session:
            session.run(query, params)
            return True
    
    def clear_database(self) -> None:
        """清空数据库中的所有节点和关系"""
        query = "MATCH (n) DETACH DELETE n"
        
        with self.driver.session() as session:
            session.run(query)
            logging.info("已清空数据库")
    
    def import_from_json(self, json_data: Dict, format_type: str = "fusion360") -> str:
        """
        从JSON数据导入装配体结构
        
        Args:
            json_data: 装配体JSON数据
            format_type: 数据格式类型，例如 "fusion360", "onshape" 等
            
        Returns:
            顶层装配体ID
        """
        start_time = time.time()
        # 使用适当的转换器将源数据转换为统一格式
        converter = get_converter(format_type)
        cad_data = converter.convert(json_data)
        
        # 导入统一格式数据
        assembly_id = self.import_from_unified_format(cad_data)
        
        end_time = time.time()
        execution_time = end_time - start_time
        logging.info(f"从JSON导入装配体总耗时: {execution_time:.4f}秒")
        return assembly_id
    
    def import_from_unified_format(self, cad_data: CADModelData) -> str:
        """
        从统一格式数据导入装配体结构
        
        Args:
            cad_data: 统一格式的CAD模型数据
            
        Returns:
            顶层装配体ID
        """
        start_time = time.time()
        assembly = cad_data.assembly
        
        # 创建顶层装配体
        assembly_id = self.create_assembly(
            name=assembly.name,
            area=assembly.area,
            volume=assembly.volume,
            density=assembly.density,
            mass=assembly.mass,
            industry=assembly.industry,
            category=assembly.category,
            assembly_id=assembly.assembly_id,  # 使用数据对象自带的ID
            shape_embedding=assembly.shape_embedding
        )
        
        if not assembly_id:
            raise RuntimeError("创建顶层装配体失败")
        
        # 处理子装配体
        for subassembly in assembly.subassemblies:
            self._import_subassembly(subassembly, assembly_id)
        
        # 处理零件
        for part in assembly.parts:
            self._import_part(part, assembly_id)
        
        end_time = time.time()
        execution_time = end_time - start_time
        logging.info(f"导入装配体 {assembly.name} 总耗时: {execution_time:.4f}秒")
        return assembly_id
    
    def _import_subassembly(self, subassembly: SubAssemblyData, parent_id: str) -> str:
        """
        导入子装配体
        
        Args:
            subassembly: 子装配体数据
            parent_id: 父节点ID
            
        Returns:
            子装配体ID
        """
        # 创建子装配体
        subassembly_id = self.create_subassembly(
            name=subassembly.name,
            area=subassembly.area,
            volume=subassembly.volume,
            density=subassembly.density,
            mass=subassembly.mass,
            parent_id=parent_id,
            subassembly_id=subassembly.subassembly_id,  # 使用数据对象自带的ID
            shape_embedding=subassembly.shape_embedding
        )
        
        if not subassembly_id:
            raise RuntimeError(f"创建子装配体 {subassembly.name} 失败")
        
        # 递归处理子装配体
        for child_subassembly in subassembly.subassemblies:
            self._import_subassembly(child_subassembly, subassembly_id)
        
        # 处理零件
        for part in subassembly.parts:
            self._import_part(part, subassembly_id)
        
        return subassembly_id
    
    def _import_part(self, part: PartData, parent_id: str) -> str:
        """
        导入零件
        
        Args:
            part: 零件数据
            parent_id: 父节点ID
            
        Returns:
            零件ID
        """
        # 创建零件
        part_id = self.create_part(
            name=part.name,
            area=part.area,
            volume=part.volume,
            density=part.density,
            mass=part.mass,
            parent_id=parent_id,
            material=part.material,
            part_id=part.part_id,  # 使用数据对象自带的ID
            shape_embedding=part.shape_embedding
        )
        
        if not part_id:
            raise RuntimeError(f"创建零件 {part.name} 失败")
        
        # 处理特征
        for feature in part.features:
            self._import_feature(feature, part_id)
        
        return part_id
    
    def _import_feature(self, feature: FeatureData, parent_id: str) -> str:
        """
        导入特征
        
        Args:
            feature: 特征数据
            parent_id: 父节点ID
        
        Returns:
            特征ID
        """
        # 创建特征
        feature_id = self.create_feature(
            length=feature.length,
            diameter=feature.diameter,
            name=feature.name,
            type=feature.type,
            parent_id=parent_id,
            feature_id=feature.feature_id  # 使用数据对象自带的ID
        )
        
        if not feature_id:
            raise RuntimeError(f"创建特征 {feature.name} 失败")
        
        return feature_id 
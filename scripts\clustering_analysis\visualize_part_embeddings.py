#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用不同的降维算法(TSNE, PCA, UMAP)对CLIP形状向量进行可视化。

用法示例：

# 默认参数使用UMAP进行降维
python scripts/clustering_analysis/visualize_part_embeddings.py

# 指定特征向量路径和输出目录
python scripts/clustering_analysis/visualize_part_embeddings.py \
    --feature_path dataset/clip_features.pkl \
    --output_dir visualization_results
"""
import os
import sys
import pickle
import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import umap
from pathlib import Path
import random
from PIL import Image
import matplotlib.gridspec as gridspec

def load_features(feature_path="dataset/clip_features.pkl"):
    """
    加载CLIP特征向量
    
    参数:
        feature_path (str): 特征向量文件路径
        
    返回:
        dict: 加载的特征向量字典
    """
    print(f"正在加载特征向量: {feature_path}")
    with open(feature_path, 'rb') as f:
        features = pickle.load(f)
    
    return features

def prepare_data_for_visualization(features):
    """
    准备用于可视化的数据
    
    参数:
        features (dict): 特征向量字典
        
    返回:
        tuple: (展平的特征向量数组, 零件名称列表, 装配体名称列表)
    """
    all_features = []
    all_part_names = []
    all_assembly_names = []
    
    for assembly_name, parts in features.items():
        for part_name, feature_vector in parts.items():
            # 确保特征向量是一维数组
            if len(feature_vector.shape) > 1 and feature_vector.shape[0] == 1:
                feature_vector = feature_vector[0]
            
            all_features.append(feature_vector)
            all_part_names.append(part_name)
            all_assembly_names.append(assembly_name)
    
    return np.array(all_features), all_part_names, all_assembly_names

def apply_dimensionality_reduction(features, method='tsne', n_components=2, random_state=42):
    """
    应用降维算法
    
    参数:
        features (numpy.ndarray): 特征向量数组
        method (str): 降维方法，可选 'tsne', 'pca', 或 'umap'
        n_components (int): 降维后的维度
        random_state (int): 随机种子
        
    返回:
        numpy.ndarray: 降维后的特征向量
    """
    print(f"使用 {method.upper()} 进行降维到 {n_components} 维...")
    
    if method.lower() == 'tsne':
        reducer = TSNE(n_components=n_components, random_state=random_state, perplexity=30, n_iter=2000)
    elif method.lower() == 'pca':
        reducer = PCA(n_components=n_components, random_state=random_state)
    elif method.lower() == 'umap':
        reducer = umap.UMAP(n_components=n_components, random_state=random_state)
    else:
        raise ValueError(f"不支持的降维方法: {method}")
    
    reduced_features = reducer.fit_transform(features)
    return reduced_features

def visualize_2d(reduced_features, part_names, assembly_names, output_dir="visualization_results", 
                color_by='assembly', figsize=(12, 10), save_fig=True, interactive=False, 
                max_parts_to_label=50, original_data_dir=None):
    """
    2D可视化降维后的特征向量
    
    参数:
        reduced_features (numpy.ndarray): 降维后的特征向量
        part_names (list): 零件名称列表
        assembly_names (list): 装配体名称列表
        output_dir (str): 输出目录
        color_by (str): 着色方式，'assembly'或'random'
        figsize (tuple): 图形大小
        save_fig (bool): 是否保存图形
        interactive (bool): 是否添加交互功能
        max_parts_to_label (int): 最大标记零件数量
        original_data_dir (str): 原始数据目录，用于显示零件图片
    """
    os.makedirs(output_dir, exist_ok=True)
    
    plt.figure(figsize=figsize)
    ax = plt.subplot(111)
    
    # 根据装配体或随机分配颜色
    if color_by == 'assembly':
        unique_assemblies = list(set(assembly_names))
        color_dict = {assembly: plt.cm.tab20(i % 20) for i, assembly in enumerate(unique_assemblies)}
        colors = [color_dict[assembly] for assembly in assembly_names]
        legend_elements = [plt.Line2D([0], [0], marker='o', color='w', 
                          label=assembly[:15] + ('...' if len(assembly) > 15 else ''), 
                          markerfacecolor=color, markersize=8) 
                          for assembly, color in color_dict.items()]
        legend_title = "装配体"
    else:  # random coloring
        colors = [plt.cm.tab20(random.randint(0, 19)) for _ in range(len(part_names))]
        legend_elements = []
        legend_title = None
    
    # 绘制散点图
    scatter = ax.scatter(reduced_features[:, 0], reduced_features[:, 1], c=colors, alpha=0.7)
    
    # 添加标签（如果零件数量不太多）
    if len(part_names) <= max_parts_to_label:
        for i, (x, y) in enumerate(reduced_features):
            plt.annotate(part_names[i], (x, y), fontsize=8)
    
    # 添加图例
    if legend_elements:
        if len(legend_elements) > 20:
            # 如果装配体太多，只显示前20个
            legend_elements = legend_elements[:20]
            plt.legend(handles=legend_elements, title=legend_title, loc='best', fontsize='small')
        else:
            plt.legend(handles=legend_elements, title=legend_title, loc='best')
    
    plt.title("零件形状向量的二维降维可视化")
    plt.xlabel("维度 1")
    plt.ylabel("维度 2")
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 保存图形
    if save_fig:
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "part_vectors_2d.png"), dpi=300)
        print(f"已保存可视化图片到 {os.path.join(output_dir, 'part_vectors_2d.png')}")
    
    # 添加交互功能（如果启用）
    if interactive and original_data_dir:
        def on_click(event):
            if event.inaxes != ax:
                return
            
            # 查找最近的点
            distances = np.sqrt((reduced_features[:, 0] - event.xdata)**2 + 
                               (reduced_features[:, 1] - event.ydata)**2)
            closest_idx = np.argmin(distances)
            
            # 获取点的信息
            assembly = assembly_names[closest_idx]
            part = part_names[closest_idx]
            
            # 查找并显示零件图片
            img_path = os.path.join(original_data_dir, assembly, f"{part}.png")
            if os.path.exists(img_path):
                plt.figure(figsize=(5, 5))
                plt.imshow(Image.open(img_path))
                plt.title(f"装配体: {assembly}\n零件: {part}")
                plt.axis('off')
                plt.show()
            else:
                print(f"找不到图片: {img_path}")
        
        plt.gcf().canvas.mpl_connect('button_press_event', on_click)
        print("提示: 点击图上的点可以查看对应的零件图片")
    
    plt.show()

def visualize_3d(reduced_features, part_names, assembly_names, output_dir="visualization_results", 
                color_by='assembly', figsize=(12, 10), save_fig=True):
    """
    3D可视化降维后的特征向量
    
    参数:
        reduced_features (numpy.ndarray): 降维后的特征向量
        part_names (list): 零件名称列表
        assembly_names (list): 装配体名称列表
        output_dir (str): 输出目录
        color_by (str): 着色方式，'assembly'或'random'
        figsize (tuple): 图形大小
        save_fig (bool): 是否保存图形
    """
    os.makedirs(output_dir, exist_ok=True)
    
    fig = plt.figure(figsize=figsize)
    ax = fig.add_subplot(111, projection='3d')
    
    # 根据装配体或随机分配颜色
    if color_by == 'assembly':
        unique_assemblies = list(set(assembly_names))
        color_dict = {assembly: plt.cm.tab20(i % 20) for i, assembly in enumerate(unique_assemblies)}
        colors = [color_dict[assembly] for assembly in assembly_names]
        legend_elements = [plt.Line2D([0], [0], marker='o', color='w', 
                          label=assembly[:15] + ('...' if len(assembly) > 15 else ''), 
                          markerfacecolor=color, markersize=8) 
                          for assembly, color in color_dict.items()]
        legend_title = "装配体"
    else:  # random coloring
        colors = [plt.cm.tab20(random.randint(0, 19)) for _ in range(len(part_names))]
        legend_elements = []
        legend_title = None
    
    # 绘制3D散点图
    scatter = ax.scatter(
        reduced_features[:, 0], 
        reduced_features[:, 1], 
        reduced_features[:, 2], 
        c=colors, 
        alpha=0.7
    )
    
    # 添加图例
    if legend_elements:
        if len(legend_elements) > 20:
            # 如果装配体太多，只显示前20个
            legend_elements = legend_elements[:20]
            plt.legend(handles=legend_elements, title=legend_title, loc='best', fontsize='small')
        else:
            plt.legend(handles=legend_elements, title=legend_title, loc='best')
    
    plt.title("零件形状向量的三维降维可视化")
    ax.set_xlabel("维度 1")
    ax.set_ylabel("维度 2")
    ax.set_zlabel("维度 3")
    
    # 保存图形
    if save_fig:
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "part_vectors_3d.png"), dpi=300)
        print(f"已保存可视化图片到 {os.path.join(output_dir, 'part_vectors_3d.png')}")
    
    plt.show()

def main():
    # 配置参数
    feature_path = "dataset/clip_features.pkl"  # 特征向量文件路径
    output_dir = "visualization_results"  # 输出目录
    
    # 加载特征向量
    features = load_features(feature_path)
    
    # 准备数据
    feature_array, part_names, assembly_names = prepare_data_for_visualization(features)
    print(f"加载了 {len(feature_array)} 个零件的特征向量")
    
    # 降维到2D并可视化 (使用随机着色，关注零件间相似度)
    # reduced_features_2d = apply_dimensionality_reduction(feature_array, method='tsne', n_components=2)
    # visualize_2d(reduced_features_2d, part_names, assembly_names, 
    #             output_dir=output_dir, color_by='random',
    #             save_fig=True, interactive=False, 
    #             max_parts_to_label=50)
    
    # # 降维到3D并可视化 (使用随机着色，关注零件间相似度)
    # reduced_features_3d = apply_dimensionality_reduction(feature_array, method='tsne', n_components=3)
    # visualize_3d(reduced_features_3d, part_names, assembly_names, 
    #             output_dir=output_dir, color_by='random',
    #             save_fig=True)
    
    # 使用UMAP降维并可视化 (使用随机着色，关注零件间相似度)
    print("\n尝试使用UMAP进行降维...")
    try:
        reduced_features_umap = apply_dimensionality_reduction(feature_array, method='umap', n_components=2)
        visualize_2d(reduced_features_umap, part_names, assembly_names, 
                     output_dir=output_dir, save_fig=True,
                     color_by='random')
    except Exception as e:
        print(f"UMAP降维失败: {e}")
    
    print("\n降维可视化完成!")

if __name__ == "__main__":
    main() 
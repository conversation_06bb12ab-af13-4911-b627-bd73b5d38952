import psycopg2
from psycopg2 import OperationalError

def test_postgres_connection(host, port, dbname, user, password):
    try:
        conn = psycopg2.connect(
            host=host,
            port=port,
            dbname=dbname,
            user=user,
            password=password
        )
        conn.close()
        print("PostgreSQL 连接成功！")
        return True
    except OperationalError as e:
        print(f"连接失败: {e}")
        return False

# 示例用法
if __name__ == "__main__":
    test_postgres_connection(
        host="*************",
        port=5434,
        dbname="postgres",
        user="supabase_admin",
        password="123456"
    )



import os
import json

def get_fusion360_test_paths(dataset_dir="datasets/fusion360_assembly"):
    """
    获取Fusion360数据集中测试集的文件夹路径
    
    参数:
        dataset_dir (str): Fusion360数据集文件夹路径
        
    返回:
        list: 所有测试集文件夹的完整路径列表
    """
    # 读取train_test.json文件
    json_path = os.path.join(dataset_dir, 'train_test.json')
    
    with open(json_path, 'r') as f:
        data = json.load(f)
    
    # 获取test数组
    test_folders = data.get('test', [])
    
    # 拼接路径
    test_paths = [os.path.join(dataset_dir, folder) for folder in test_folders]
    
    return test_paths

from abc import ABC, abstractmethod
import uuid
from typing import Dict, Any, List, Union
from src.data_formats import CADModelData, AssemblyData, SubAssemblyData, PartData, FeatureData

class BaseDataConverter(ABC):
    """数据转换器基类，用于将不同格式的CAD数据转换为统一格式"""
    
    @abstractmethod
    def convert(self, source_data: Dict[str, Any]) -> CADModelData:
        """
        将源数据转换为统一的CAD模型数据格式
        
        Args:
            source_data: 源数据字典
            
        Returns:
            CADModelData: 统一格式的CAD模型数据
        """
        pass


class Fusion360Converter(BaseDataConverter):
    """Fusion360数据转换器"""
    
    def convert(self, source_data: Dict[str, Any]) -> CADModelData:
        """
        将Fusion360格式的数据转换为统一的CAD模型数据格式
        
        Args:
            source_data: Fusion360源数据字典，格式如下：
            {
                "tree": {
                    "root": {
                        "id1": { ... },
                        "id2": { ... },
                        ...
                    }
                }
            }
            
        Returns:
            CADModelData: 统一格式的CAD模型数据
        """
        if not source_data or 'tree' not in source_data or 'root' not in source_data['tree']:
            raise ValueError("无效的Fusion360数据：缺少tree.root节点")
        
        # 创建顶层装配体
        assembly_id = str(uuid.uuid4())
        assembly = AssemblyData(
            assembly_id=assembly_id,
            name="Root Assembly",
            area=1000.0,  # 默认值，实际应用中应从数据中提取
            volume=2000.0,
            density=7.8,
            mass=15600.0
        )
        
        # 递归处理子节点
        root_data = source_data['tree']['root']
        self._process_tree_nodes(root_data, assembly)
        
        return CADModelData(
            assembly=assembly,
            source_format="fusion360",
            metadata={"original_data_structure": "tree.root"}
        )
    
    def _process_tree_nodes(self, nodes: Dict, parent: Union[AssemblyData, SubAssemblyData]) -> None:
        """
        递归处理Fusion360树节点
        
        Args:
            nodes: 节点字典
            parent: 父节点（装配体或子装配体）
        """
        for node_id, node_data in nodes.items():
            # 判断是子装配体还是零件
            is_subassembly = bool(node_data)  # 如果有子节点，则为子装配体
            
            if is_subassembly:
                # 创建子装配体
                subassembly = SubAssemblyData(
                    subassembly_id=str(uuid.uuid4()),
                    name=f"SubAssembly-{node_id[:8]}",  # 使用ID的前8位作为名称
                    area=parent.area * 0.8,
                    volume=parent.volume * 0.8,
                    density=parent.density,
                    mass=parent.mass * 0.8
                )
                
                # 添加到父节点
                parent.subassemblies.append(subassembly)
                
                # 递归处理子节点
                self._process_tree_nodes(node_data, subassembly)
            else:
                # 创建零件
                part = PartData(
                    part_id=str(uuid.uuid4()),
                    name=f"Part-{node_id[:8]}",  # 使用ID的前8位作为名称
                    area=parent.area * 0.5,
                    volume=parent.volume * 0.5,
                    density=parent.density,
                    mass=parent.mass * 0.5
                )
                
                # 添加到父节点
                parent.parts.append(part)
                
                # 为零件添加一个示例特征
                feature = FeatureData(
                    feature_id=str(uuid.uuid4()),
                    feature_type="孔",
                    length=50.0,
                    width=30.0
                )
                part.features.append(feature)


# 可以根据需要添加其他CAD系统的转换器
class OnshapeConverter(BaseDataConverter):
    """Onshape数据转换器"""
    
    def convert(self, source_data: Dict[str, Any]) -> CADModelData:
        """将Onshape格式的数据转换为统一的CAD模型数据格式"""
        # TODO: 实现Onshape数据转换逻辑
        raise NotImplementedError("Onshape转换器尚未实现")


# 工厂方法，用于获取适当的转换器
def get_converter(source_format: str) -> BaseDataConverter:
    """
    获取指定格式的数据转换器
    
    Args:
        source_format: 源数据格式，例如 "fusion360", "onshape"
        
    Returns:
        BaseDataConverter: 对应的数据转换器实例
    """
    converters = {
        "fusion360": Fusion360Converter(),
        "onshape": OnshapeConverter()
    }
    
    converter = converters.get(source_format.lower())
    if not converter:
        raise ValueError(f"不支持的数据格式: {source_format}")
    
    return converter 
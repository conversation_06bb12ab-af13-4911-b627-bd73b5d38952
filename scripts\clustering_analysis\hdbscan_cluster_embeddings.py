#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用 HDBSCAN 对 CLIP 形状向量进行无监督聚类，并给出常见内部评估指标。

用法示例：

# 默认参数，euclidean 距离 + L2 归一化
python scripts/clustering_analysis/hdbscan_cluster_embeddings.py \
    --embed dataset/clip_features.pkl \
    --out dataset/hdbscan_labels.pkl

# 指定 min_cluster_size、关闭归一化
python scripts/clustering_analysis/hdbscan_cluster_embeddings.py \
    --min_cluster_size 20 --norm 0
"""
from __future__ import annotations

import argparse
import pickle
from collections import Counter
from pathlib import Path
from typing import Tuple

import numpy as np
from sklearn.preprocessing import normalize
from sklearn import metrics

try:
    import hdbscan  # type: ignore
except ImportError as e:
    raise ImportError(
        "未安装 hdbscan。请先运行 `pip install hdbscan` 或 `conda install -c conda-forge hdbscan`"
    ) from e


def load_embeddings(pkl_path: Path) -> Tuple[np.ndarray, list[str], list[str]]:
    """读取向量并展开为 (N, D)，同时返回零件名和装配名列表"""
    with pkl_path.open("rb") as f:
        data: dict = pickle.load(f)

    feats: list[np.ndarray] = []
    part_names: list[str] = []
    assembly_names: list[str] = []
    for assembly, parts in data.items():
        for part, vec in parts.items():
            if vec.ndim > 1:
                vec = vec.squeeze()
            feats.append(vec)
            part_names.append(part)
            assembly_names.append(assembly)
    return np.vstack(feats), part_names, assembly_names


def run_hdbscan(
    X: np.ndarray,
    min_cluster_size: int = 15,
    min_samples: int | None = None,
    metric: str = "cosine",
    **kwargs,
) -> hdbscan.HDBSCAN:
    clusterer = hdbscan.HDBSCAN(
        min_cluster_size=min_cluster_size,
        min_samples=min_samples,
        metric=metric,
        cluster_selection_method="eom",
        **kwargs,
    )
    clusterer.fit(X)
    return clusterer


def eval_internal_metrics(X: np.ndarray, labels: np.ndarray) -> dict[str, float | str]:
    """计算 silhouette、DBI、CHI；若簇数不足 2，则返回 N/A"""
    # 过滤掉噪声标签 -1
    mask = labels != -1
    if mask.sum() < 2 or len(set(labels[mask])) < 2:
        return {"silhouette": "N/A", "dbi": "N/A", "chi": "N/A"}

    try:
        sil = metrics.silhouette_score(X[mask], labels[mask], metric="cosine")
    except Exception:
        sil = "err"
    try:
        dbi = metrics.davies_bouldin_score(X[mask], labels[mask])
    except Exception:
        dbi = "err"
    try:
        chi = metrics.calinski_harabasz_score(X[mask], labels[mask])
    except Exception:
        chi = "err"
    return {"silhouette": sil, "dbi": dbi, "chi": chi}


def main():
    parser = argparse.ArgumentParser(description="使用 HDBSCAN 对零件形状向量聚类")
    parser.add_argument("--embed", default="dataset/clip_features.pkl", help="向量 pickle 文件路径")
    parser.add_argument("--out", default="dataset/hdbscan_labels.pkl", help="输出标签 pickle 文件")
    parser.add_argument("--min_cluster_size", type=int, default=15, help="HDBSCAN 的 min_cluster_size")
    parser.add_argument("--min_samples", type=int, default=None, help="HDBSCAN 的 min_samples")
    parser.add_argument("--metric", default="euclidean", help="距离度量，默认 euclidean")
    parser.add_argument("--norm", type=int, choices=[0, 1], default=1, help="是否做 L2 归一化 (1/0)")
    args = parser.parse_args()

    embed_path = Path(args.embed)
    if not embed_path.exists():
        raise FileNotFoundError(f"未找到向量文件: {embed_path}")

    # 载入向量
    X, part_names, assembly_names = load_embeddings(embed_path)
    print(f"载入 {X.shape[0]} 个向量，维度 {X.shape[1]}")

    # 可选归一化
    if args.norm:
        X = normalize(X)
        print("已做 L2 归一化")

    # 聚类
    print("开始 HDBSCAN 聚类...")
    clusterer = run_hdbscan(
        X,
        min_cluster_size=args.min_cluster_size,
        min_samples=args.min_samples,
        metric=args.metric,
    )
    labels = clusterer.labels_
    n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
    noise_ratio = (labels == -1).mean()
    print(f"聚类完成！簇数: {n_clusters} | 噪声比例: {noise_ratio:.2%}")

    # 评估指标
    metrics_dict = eval_internal_metrics(X, labels)
    print("内部指标：")
    for k, v in metrics_dict.items():
        print(f"  {k}: {v}")

    # 簇大小分布
    cnt = Counter(labels)
    if -1 in cnt:
        cnt.pop(-1)
    print("前 10 大簇 (簇ID:数量):", cnt.most_common(10))

    # 保存结果
    out_path = Path(args.out)
    with out_path.open("wb") as f:
        pickle.dump({
            "labels": labels,
            "part_names": part_names,
            "assembly_names": assembly_names,
            "params": {
                "min_cluster_size": args.min_cluster_size,
                "min_samples": args.min_samples,
                "metric": args.metric,
                "norm": bool(args.norm),
            },
            "cluster_persistence": getattr(clusterer, "cluster_persistence_", None),
        }, f)
    print(f"已将标签保存到 {out_path}")


if __name__ == "__main__":
    main() 
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可视化 HDBSCAN 聚类结果，为每个簇生成图片网格。

用法示例：
python scripts/visualize_hdbscan_clusters.py \
    --labels dataset/hdbscan_labels.pkl \
    --img_dir datasets/fusion360_assembly \
    --out_dir visualization_results/clusters

可视化选项：
--top_n 10          # 只显示前10个最大的簇
--samples_per_cluster 16  # 每个簇展示16张图片
--min_cluster_size 20     # 只显示大于20个样本的簇
"""
import argparse
import os
import pickle
import random
from collections import defaultdict
from pathlib import Path

import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from tqdm import tqdm


def load_clustering_results(labels_path):
    """加载聚类结果"""
    with open(labels_path, 'rb') as f:
        data = pickle.load(f)
    
    labels = data['labels']
    part_names = data['part_names']
    assembly_names = data['assembly_names']
    
    # 构建每个簇的样本索引
    clusters = defaultdict(list)
    for i, label in enumerate(labels):
        if label != -1:  # 忽略噪声点
            clusters[int(label)].append(i)
    
    return clusters, labels, part_names, assembly_names, data.get('params', {})


def get_cluster_stats(clusters):
    """获取簇的统计信息"""
    stats = []
    for cluster_id, indices in clusters.items():
        stats.append((cluster_id, len(indices)))
    
    # 按簇大小排序
    stats.sort(key=lambda x: x[1], reverse=True)
    return stats


def create_cluster_grid(cluster_indices, part_names, assembly_names, img_dir, 
                        samples_per_cluster=16, grid_size=None):
    """为一个簇创建图片网格"""
    if grid_size is None:
        # 自动计算网格大小
        grid_size = int(np.ceil(np.sqrt(samples_per_cluster)))
    
    # 如果簇太大，随机采样
    if len(cluster_indices) > samples_per_cluster:
        sample_indices = random.sample(cluster_indices, samples_per_cluster)
    else:
        sample_indices = cluster_indices
    
    # 创建网格
    fig = plt.figure(figsize=(grid_size*2, grid_size*2))
    
    for i, idx in enumerate(sample_indices):
        if i >= samples_per_cluster:
            break
            
        assembly = assembly_names[idx]
        part = part_names[idx]
        
        # 构建图片路径
        img_path = os.path.join(img_dir, assembly, f"{part}.png")
        
        if os.path.exists(img_path):
            try:
                # 添加子图
                ax = fig.add_subplot(grid_size, grid_size, i + 1)
                img = Image.open(img_path)
                ax.imshow(img)
                ax.axis('off')
                ax.set_title(f"{part}", fontsize=8)
            except Exception as e:
                print(f"无法加载图片 {img_path}: {e}")
        else:
            # 如果图片不存在，显示空白
            ax = fig.add_subplot(grid_size, grid_size, i + 1)
            ax.text(0.5, 0.5, f"找不到图片\n{part}", 
                   ha='center', va='center', fontsize=8)
            ax.axis('off')
    
    plt.tight_layout()
    return fig


def visualize_clusters(clusters, part_names, assembly_names, img_dir, out_dir,
                       top_n=None, samples_per_cluster=16, min_cluster_size=0):
    """可视化多个簇"""
    # 获取簇统计信息
    stats = get_cluster_stats(clusters)
    
    # 过滤小簇
    if min_cluster_size > 0:
        stats = [(cid, size) for cid, size in stats if size >= min_cluster_size]
    
    # 限制簇数量
    if top_n is not None:
        stats = stats[:top_n]
    
    # 创建输出目录
    os.makedirs(out_dir, exist_ok=True)
    
    # 为每个簇创建可视化
    for cluster_id, size in tqdm(stats, desc="生成簇可视化"):
        cluster_indices = clusters[cluster_id]
        
        # 创建图片网格
        fig = create_cluster_grid(
            cluster_indices, part_names, assembly_names, img_dir, 
            samples_per_cluster=samples_per_cluster
        )
        
        # 设置标题
        plt.suptitle(f"簇 #{cluster_id} - 大小: {size}", fontsize=16)
        
        # 保存图片
        out_path = os.path.join(out_dir, f"cluster_{cluster_id:03d}_{size}.png")
        plt.savefig(out_path, dpi=150)
        plt.close(fig)
        
    # 创建索引HTML文件
    create_html_index(out_dir, stats)
    
    return len(stats)


def create_html_index(out_dir, stats):
    """创建HTML索引文件，方便浏览所有簇"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>HDBSCAN 聚类结果可视化</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .cluster-grid {{ display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }}
            .cluster-item {{ border: 1px solid #ddd; padding: 10px; border-radius: 5px; }}
            .cluster-item img {{ max-width: 100%; height: auto; }}
            h1, h2 {{ color: #333; }}
        </style>
    </head>
    <body>
        <h1>HDBSCAN 聚类结果可视化</h1>
        <p>共 {len(stats)} 个簇</p>
        <div class="cluster-grid">
    """
    
    for cluster_id, size in stats:
        img_filename = f"cluster_{cluster_id:03d}_{size}.png"
        html_content += f"""
        <div class="cluster-item">
            <h2>簇 #{cluster_id} (大小: {size})</h2>
            <a href="{img_filename}" target="_blank">
                <img src="{img_filename}" alt="簇 {cluster_id}">
            </a>
        </div>
        """
    
    html_content += """
        </div>
    </body>
    </html>
    """
    
    with open(os.path.join(out_dir, "index.html"), "w", encoding="utf-8") as f:
        f.write(html_content)


def main():
    parser = argparse.ArgumentParser(description="可视化 HDBSCAN 聚类结果")
    parser.add_argument("--labels", default="dataset/hdbscan_labels.pkl", help="聚类标签文件路径")
    parser.add_argument("--img_dir", default="datasets/fusion360_assembly", help="原始图片目录")
    parser.add_argument("--out_dir", default="visualization_results/clusters", help="输出目录")
    parser.add_argument("--top_n", type=int, default=None, help="只显示前N个最大的簇")
    parser.add_argument("--samples_per_cluster", type=int, default=16, help="每个簇显示的样本数")
    parser.add_argument("--min_cluster_size", type=int, default=0, help="最小簇大小过滤")
    args = parser.parse_args()
    
    # 加载聚类结果
    print(f"加载聚类结果: {args.labels}")
    clusters, labels, part_names, assembly_names, params = load_clustering_results(args.labels)
    
    # 打印参数
    print("聚类参数:")
    for k, v in params.items():
        print(f"  {k}: {v}")
    
    # 统计信息
    n_clusters = len(clusters)
    n_noise = np.sum(labels == -1)
    n_total = len(labels)
    
    print(f"共 {n_clusters} 个簇, {n_noise} 个噪声点 ({n_noise/n_total:.2%})")
    
    # 可视化簇
    print(f"开始生成可视化到: {args.out_dir}")
    visualized = visualize_clusters(
        clusters, part_names, assembly_names, args.img_dir, args.out_dir,
        top_n=args.top_n, 
        samples_per_cluster=args.samples_per_cluster,
        min_cluster_size=args.min_cluster_size
    )
    
    print(f"已生成 {visualized} 个簇的可视化")
    print(f"请在浏览器中打开 {os.path.join(args.out_dir, 'index.html')} 查看结果")


if __name__ == "__main__":
    main() 
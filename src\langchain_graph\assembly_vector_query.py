import os
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
from langchain.vectorstores.neo4j_vector import Neo4jVector
from config import Config

from langchain.embeddings import HuggingFaceEmbeddings

embedding_model = HuggingFaceEmbeddings(model_name="sentence-transformers/all-MiniLM-L6-v2")

# 获取Neo4j数据库配置
db_config = Config.get_neo4j_config()

# 创建Neo4j向量索引对象，针对装配体节点
vector_index = Neo4jVector.from_existing_graph(
    embedding_model,
    url=db_config['uri'],
    username=db_config['user'],
    password=db_config['password'],
    index_name='assembly_vectors',  # 向量索引名称
    node_label='Assembly',          # 节点标签为装配体
    text_node_properties=['name', 'industry', 'category', 'mass'],  # 用于嵌入的文本属性
    embedding_node_property='test_embedding',                    # 嵌入存储属性
)

# 示例：查询与输入文本最相似的装配体节点
query_text = "Assemblies with flaps"
response = vector_index.similarity_search(query_text)

if response:
    print("最相关的装配体节点：")
    for doc in response:
        print(doc.page_content)
else:
    print("未找到相关装配体节点。") 



import os
import sys
import argparse
import pickle
import numpy as np
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.database.milvus_utils import MilvusHandler


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='从Milvus数据库中搜索相似零件')
    parser.add_argument('--assembly_id', type=str, required=True,
                        help='要查询的装配体ID')
    parser.add_argument('--part_id', type=str, required=True,
                        help='要查询的零件ID')
    parser.add_argument('--pkl_path', type=str, default='dataset/clip_features.pkl',
                        help='CLIP特征的pickle文件路径')
    parser.add_argument('--collection', type=str, default='parts',
                        help='Milvus集合名称')
    parser.add_argument('--host', type=str, default='127.0.0.1',
                        help='Milvus服务器主机')
    parser.add_argument('--port', type=str, default='19530',
                        help='Milvus服务器端口')
    parser.add_argument('--top_k', type=int, default=5,
                        help='返回的最相似结果数量')
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()
    
    # 确保pickle文件存在
    if not os.path.exists(args.pkl_path):
        print(f"错误：文件 '{args.pkl_path}' 不存在")
        return 1
    
    try:
        # 从pickle文件获取查询向量
        with open(args.pkl_path, 'rb') as f:
            data = pickle.load(f)
        
        if args.assembly_id not in data:
            print(f"错误：找不到装配体ID '{args.assembly_id}'")
            return 1
            
        if args.part_id not in data[args.assembly_id]:
            print(f"错误：在装配体 '{args.assembly_id}' 中找不到零件ID '{args.part_id}'")
            return 1
            
        query_embedding = data[args.assembly_id][args.part_id]
        
        # 创建Milvus处理器
        handler = MilvusHandler(host=args.host, port=args.port)
        
        # 执行搜索
        print(f"在集合 '{args.collection}' 中搜索与零件 '{args.part_id}' 相似的top {args.top_k} 个零件...")
        results = handler.search_similar_parts(
            collection_name=args.collection,
            query_embedding=query_embedding,
            top_k=args.top_k
        )
        
        # 显示结果
        if not results:
            print("没有找到相似零件")
            return 0
            
        print("\n搜索结果:")
        print("距离\t零件ID\t装配体ID")
        print("------------------------------")
        
        for hits in results:
            for hit in hits:
                print(f"{hit.distance:.4f}\t{hit.entity.get('part_id')}\t{hit.entity.get('assembly_id')}")
                
        return 0
        
    except Exception as e:
        print(f"搜索过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 